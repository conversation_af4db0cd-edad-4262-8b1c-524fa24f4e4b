import React, { useCallback } from 'react'
import { View, StyleSheet, ScrollView, Dimensions, Text, TouchableOpacity } from 'react-native'
import { IconButton } from 'react-native-paper'
import { UiPrimaryButton } from '@ui/Buttons/UiPrimaryButton'
import { UiText } from '@ui/typography/UiText'
import { authorizationService } from '@services'
import { theme } from '@theme'
import { useObserverState } from '@hooks'
import { userService } from '@services'
import { clearSavingPlanStore } from '@store/saving-plan-store'
import { clearAisProcessStore } from '@store/ais.store'
import { useRouter } from 'expo-router'
import { LineChart } from 'react-native-gifted-charts'
import { LinearGradient } from 'expo-linear-gradient'

const screenWidth = Dimensions.get("window").width

interface Transaction {
  id: number;
  type: string;
  date: string;
  amount: number;
  btc: number;
}

const mockData = {
  totalBalance: 345657.12,
  portfolio: 0.9832,
  savingsValue: 345657.12,
  depositedAmount: 250000.00,
  bitcoinValue: 366181.12,
  bitcoinChangePercent: 10.2,
  bitcoinChangeTimeframe: '1t',
  savingsChartData: [
    { value: 0, hideDataPoint: true },
    { value: 2000, hideDataPoint: true },
    { value: 30000, hideDataPoint: true },
    { value: 4000, hideDataPoint: true },
    { value: 5000, hideDataPoint: true },
    { value: 6000, hideDataPoint: true },
    { value: 7000, hideDataPoint: true },
    { value: 8000, hideDataPoint: true },
    { value: 345000, hideDataPoint: true },
    { value: 345000, hideDataPoint: true },
    { value: 345000, hideDataPoint: true },
    { value: 365000, hideDataPoint: true },
    { value: 365000 },
  ],
  depositedChartData: [
    { value: 0, hideDataPoint: true },
    { value: 1000, hideDataPoint: true },
    { value: 258000, hideDataPoint: true },
    { value: 258000, hideDataPoint: true },
    { value: 260000, hideDataPoint: true },
    { value: 260000, hideDataPoint: true },
    { value: 260000, hideDataPoint: true },
    { value: 260000, hideDataPoint: true },
    { value: 260000, hideDataPoint: true },
    { value: 262000, hideDataPoint: true },
    { value: 268000, hideDataPoint: true },
    { value: 270000, hideDataPoint: true },
    { value: 265000 },
  ],
  transactions: [
    { id: 1, type: 'Wpłata', date: '20 cze, 13:40', amount: 50, btc: 0.00013 },
    { id: 2, type: 'Wpłata', date: '19 cze, 15:40', amount: 50, btc: 0.00013 },
    { id: 3, type: 'Wpłata', date: '18 cze, 9:40', amount: 50, btc: 0.00013 },
  ] as Transaction[]
}

const FadingVerticalLines = () => {
  const numberOfLines = mockData.savingsChartData.length
  const lineStyle = {
    width: 1,
    height: 160,
  };

  return (
    <View style={styles.verticalLinesContainer}>
      {Array.from({ length: numberOfLines }).map((_, index) => (
        <LinearGradient
          key={index}
          colors={['rgba(222, 227, 225, 0)', 'rgba(222, 227, 225, 0.5)']}
          style={lineStyle}
        />
      ))}
    </View>
  );
};

const DashedBottomLine = () => {
  return (
    <View style={styles.dashedLineContainer}>
      {Array.from({ length: 40 }).map((_, index) => (
        <View key={index} style={styles.dash} />
      ))}
    </View>
  );
};

export default function Index() {
  const onPress = useCallback(async () => {
    clearSavingPlanStore();
    clearAisProcessStore();
    await authorizationService.unAuthorize()
  }, [])
  
  const router = useRouter()
  const userInfo = useObserverState(null, userService.getUserInfo())

  const NavigationBar = () => (
    <View style={styles.navigationBar}>
      <View style={styles.navigationBackground} />
      <View style={styles.navigationIndicator} />
      <View style={styles.navigationContent}>
        <View style={styles.activeNavItem}>
          <IconButton icon="home" size={24} iconColor={theme.colors.primary} />
          <Text style={styles.activeNavText}>Start</Text>
          <View style={styles.activeNavLine} />
        </View>
        <View style={styles.navItem}>
          <IconButton icon="swap-horizontal" size={24} iconColor="#8A9E96" />
          <Text style={styles.navText}>Transakcje</Text>
        </View>
        <View style={styles.navItem}>
          <IconButton icon="wallet" size={24} iconColor="#8A9E96" />
          <Text style={styles.navText}>Portfel</Text>
        </View>
        <View style={styles.navItem}>
          <IconButton icon="book-open" size={24} iconColor="#8A9E96" />
          <Text style={styles.navText}>Czytelnia</Text>
        </View>
        <View style={styles.navItem}>
          <IconButton icon="dots-grid" size={24} iconColor="#8A9E96" />
          <Text style={styles.navText}>Więcej</Text>
        </View>
      </View>
    </View>
  )

  const ConfigurationBanner = () => (
    <View style={styles.configBanner}>
      <View style={styles.configContent}>
        <UiText translatedText="dashboard.configurationBanner.text" variant="bodyMedium" style={styles.configText} />
        <UiPrimaryButton
          translationTitle="dashboard.configurationBanner.button"
          mode="contained"
          onPress={() => {/* TODO: Add navigation logic */}}
          containerStyle={{ width: 'auto' }}
          buttonStyle={styles.configButton}
          contentStyle={styles.configButtonContent}
          labelStyle={styles.configButtonText}
        />
      </View>
      <View style={styles.configImages}>
        <View style={[styles.configImage, { backgroundColor: '#40C5B9' }]} />
        <View style={[styles.configImage, { backgroundColor: '#1FAD8C' }]} />
        <View style={[styles.configImage, { backgroundColor: '#FFB800' }]} />
      </View>
      <View style={styles.configIndicators}>
        <View style={[styles.indicator, { backgroundColor: '#DEE3E1' }]} />
        <View style={[styles.indicator, { backgroundColor: '#1FAD8C', width: 6, height: 6 }]} />
        <View style={[styles.indicator, { backgroundColor: '#DEE3E1' }]} />
      </View>
    </View>
  )

  const SavingsChart = () => {
    const numberOfPoints = mockData.savingsChartData.length;
    const containerWidth = screenWidth - 64; // Account for card padding
    const calculatedSpacing = numberOfPoints > 1 ? containerWidth / (numberOfPoints - 1) : 0;
    
    // Calculate the value range for proper vertical scaling
    const allValues = [...mockData.savingsChartData.map(d => d.value), ...mockData.depositedChartData.map(d => d.value)];
    const maxValue = Math.max(...allValues);
    const minValue = Math.min(...allValues);
    const stepValue = (maxValue - minValue) / 5; // Divide into 5 sections
    
    return (
      <View style={styles.chartCard}>
        <View style={styles.chartHeader}>
          <Text style={styles.chartAmount}>{mockData.savingsValue.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} PLN</Text>
          <Text style={styles.chartSubtext}>wpłacono {mockData.depositedAmount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} PLN</Text>
        </View>
       
        <View style={styles.chartContainer}>
          <LineChart
            areaChart
            data={mockData.savingsChartData}
            data2={mockData.depositedChartData}
            height={160}
            width={containerWidth}
            spacing={calculatedSpacing}
            maxValue={maxValue}
            mostNegativeValue={minValue}
            stepValue={stepValue}
            noOfSections={5}
            disableScroll
            color1="#F38600"
            color2="#40C1A3"
            dataPointsColor1="#F38600"
            dataPointsColor2="#40C1A3"
            dataPointsRadius={4}
            curved
            thickness1={3}
            thickness2={3}
            animateOnDataChange
            animationDuration={1000}
            startFillColor1="rgba(243, 134, 0, 0.1)"
            startFillColor2="rgba(64, 193, 163, 0.1)"
            endFillColor1="rgba(243, 134, 0, 0.02)"
            endFillColor2="rgba(64, 193, 163, 0.02)"
            startOpacity={0.1}
            endOpacity={0.02}
            initialSpacing={0}
            yAxisTextStyle={{ color: 'transparent' }}
            xAxisThickness={0}
            yAxisThickness={0}
            hideRules
            showVerticalLines={false}
            hideYAxisText
            yAxisLabelWidth={0}
            overflowBottom={-7}
            xAxisLabelsHeight={0}
          />
          <FadingVerticalLines />
          <DashedBottomLine />
        </View>
         
        <View style={styles.chartLegend}>
          <View style={styles.legendItem}>
            <View style={[styles.legendDot, { backgroundColor: '#F38600' }]} />
            <Text style={styles.legendText}>Wartość oszczędności</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendDot, { backgroundColor: '#40C1A3' }]} />
            <Text style={styles.legendText}>Wpłacone PLN</Text>
          </View>
        </View>
      </View>
    )
  }

  const BitcoinCard = () => (
    <View style={styles.bitcoinCard}>
      <View style={styles.bitcoinLeft}>
        <View style={styles.bitcoinIcon} />
        <View>
          <Text style={styles.bitcoinTitle}>Bitcoin</Text>
          <Text style={styles.bitcoinSymbol}>BTC</Text>
        </View>
      </View>
             <View style={styles.bitcoinRight}>
         <Text style={styles.bitcoinValue}>{mockData.bitcoinValue.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} PLN</Text>
         <View style={styles.bitcoinChange}>
           <Text style={styles.bitcoinChangeText}>+ {mockData.bitcoinChangePercent}% ({mockData.bitcoinChangeTimeframe})</Text>
           <IconButton icon="arrow-up-right" size={12} iconColor="#24D327" />
         </View>
       </View>
    </View>
  )

  const TransactionItem = ({ transaction }: { transaction: Transaction }) => (
    <View style={styles.transactionItem}>
      <View style={styles.transactionLeft}>
        <View style={styles.transactionIcon} />
        <View>
          <Text style={styles.transactionType}>{transaction.type}</Text>
          <Text style={styles.transactionDate}>{transaction.date}</Text>
        </View>
      </View>
      <View style={styles.transactionRight}>
        <Text style={styles.transactionAmount}>+{transaction.amount.toLocaleString('en-US')} PLN</Text>
        <Text style={styles.transactionBtc}>{transaction.btc.toLocaleString('en-US', { minimumFractionDigits: 5, maximumFractionDigits: 5 })} BTC</Text>
      </View>
    </View>
  )

  const TransactionsSection = () => (
    <View style={styles.transactionsCard}>
      <View style={styles.transactionsList}>
        {mockData.transactions.map((transaction, index) => (
          <View key={transaction.id}>
            <TransactionItem transaction={transaction} />
            {index < mockData.transactions.length - 1 && <View style={styles.transactionDivider} />}
          </View>
        ))}
      </View>
      <UiPrimaryButton
        translationTitle="dashboard.buttons.seeAll"
        mode="contained"
        onPress={() => {/* TODO: Add navigation to all transactions */}}
        containerStyle={{ width: '100%' }}
        buttonStyle={styles.seeAllButton}
        contentStyle={styles.seeAllButtonContent}
        labelStyle={styles.seeAllText}
      />
    </View>
  )

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.userInfo} onPress={() => router.push('(navigation)')}>
            <View style={styles.avatarPlaceholder} />
            <Text style={styles.greeting}>Cześć {userInfo?.username || 'Marek'}!</Text>
          </TouchableOpacity>
        </View>

        {/* Balance Section */}
        <View style={styles.balanceSection}>
          <UiText translatedText="dashboard.totalBalance" variant="titleMedium" style={styles.balanceLabel} />
          <Text style={styles.balanceAmount}>
            {Number((mockData.portfolio * mockData.bitcoinValue).toFixed(2)).toLocaleString('en-US')} PLN
          </Text>
          <Text style={styles.balanceBtc}>≈ {mockData.portfolio.toLocaleString('en-US', { minimumFractionDigits: 4, maximumFractionDigits: 4 })} BTC</Text>
        </View>

        {/* Configuration Banner */}
        <ConfigurationBanner />

        {/* Savings Section */}
        <View style={styles.section}>
          <UiText translatedText="dashboard.sections.savings" variant="titleMedium" style={styles.sectionTitle} />
          <SavingsChart />
          <BitcoinCard />
        </View>

        {/* Transactions Section */}
        <View style={styles.section}>
          <UiText translatedText="dashboard.sections.transactions" variant="titleMedium" style={styles.sectionTitle} />
          <TransactionsSection />
        </View>

        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Bottom Navigation */}
      <NavigationBar />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.lilyWhite,
  },
  scrollView: {
    flex: 1,
    paddingBottom: 80,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 50,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  avatarPlaceholder: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#DEE3E1',
  },
  greeting: {
    fontSize: 18,
    fontWeight: '400',
    color: theme.colors.text,
    lineHeight: 22,
  },
  logoutButton: {
    height: 36,
    width: 92,
    borderColor: theme.colors.inputDefaultBorderColor,
    backgroundColor: theme.colors.lilyWhite,
  },
  logoutContent: {
    height: 36,
  },
  logoutLabel: {
    marginHorizontal: 0,
    marginVertical: 0,
    fontSize: 16,
    color: theme.colors.text,
  },
  balanceSection: {
    alignItems: 'center',
    marginBottom: 64,
    marginTop: 48,
    paddingHorizontal: 16,
  },
  balanceLabel: {
    fontSize: 16,
    color: '#6F867D',
    marginBottom: 16,
  },
  balanceAmount: {
    fontSize: 40,
    fontWeight: '500',
    color: '#000000',
    letterSpacing: -1,
    textAlign: 'center',
  },
  balanceBtc: {
    fontSize: 16,
    color: '#586A63',
    marginTop: 4,
  },
  configBanner: {
    marginHorizontal: 16,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    marginBottom: 24,
    shadowColor: '#0D3D32',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
    position: 'relative',
    overflow: 'hidden',
  },
  configContent: {
    flex: 1,
    zIndex: 1,
  },
  configText: {
    fontSize: 16,
    color: '#131615',
    marginBottom: 16,
    lineHeight: 20,
  },
  configButton: {
    backgroundColor: '#1FAD8C',
    borderRadius: 32,
    alignSelf: 'flex-start',
  },
  configButtonContent: {
    height: 'auto',
    paddingVertical: 10,
    paddingHorizontal: 16,
  },
  configButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '400',
    marginHorizontal: 0,
    marginVertical: 0,
  },
  configImages: {
    position: 'absolute',
    right: 16,
    top: 16,
    flexDirection: 'column',
    gap: 8,
  },
  configImage: {
    width: 40,
    height: 40,
    borderRadius: 8,
  },
  configIndicators: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 8,
    marginTop: 16,
  },
  indicator: {
    width: 4,
    height: 4,
    borderRadius: 2,
  },
  section: {
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    color: '#6F867D',
    marginBottom: 8,
  },
  chartCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#0D3D32',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
  },
  chartHeader: {
    marginBottom: 16,
  },
  chartAmount: {
    fontSize: 24,
    fontWeight: '500',
    color: '#131615',
    marginBottom: 4,
  },
  chartSubtext: {
    fontSize: 16,
    color: '#8A9E96',
  },
  chartContainer: {
    height: 160,
    position: 'relative',
  },
  chart: {
    marginVertical: 8,
    borderRadius: 16,
  },
  chartLegend: {
    flexDirection: 'row',
    gap: 24,
    marginTop: 16,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  legendDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  legendText: {
    fontSize: 12,
    color: '#8A9E96',
  },
  bitcoinCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    shadowColor: '#0D3D32',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
  },
  bitcoinLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  bitcoinIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#F7931A',
  },
  bitcoinTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#131615',
  },
  bitcoinSymbol: {
    fontSize: 12,
    color: '#8A9E96',
  },
  bitcoinRight: {
    alignItems: 'flex-end',
  },
  bitcoinValue: {
    fontSize: 16,
    fontWeight: '500',
    color: '#131615',
  },
  bitcoinChange: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  bitcoinChangeText: {
    fontSize: 12,
    color: '#24D327',
  },
  transactionsCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    shadowColor: '#0D3D32',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
  },
  transactionsList: {
    marginBottom: 16,
  },
  transactionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  transactionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  transactionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#1FAD8C',
  },
  transactionType: {
    fontSize: 16,
    fontWeight: '500',
    color: '#131615',
  },
  transactionDate: {
    fontSize: 14,
    color: '#8A9E96',
  },
  transactionRight: {
    alignItems: 'flex-end',
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: '500',
    color: '#131615',
  },
  transactionBtc: {
    fontSize: 14,
    color: '#8A9E96',
  },
  transactionDivider: {
    height: 1,
    backgroundColor: '#F9FAFA',
    marginVertical: 8,
  },
  seeAllButton: {
    backgroundColor: '#A6B5AF',
    borderRadius: 32,
    alignItems: 'center',
  },
  seeAllButtonContent: {
    height: 'auto',
    paddingVertical: 10,
    paddingHorizontal: 16,
  },
  seeAllText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '400',
    marginHorizontal: 0,
    marginVertical: 0,
  },
  navigationBar: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 80,
  },
  navigationBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#FFFFFF',
    shadowColor: '#0D3D32',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  navigationIndicator: {
    position: 'absolute',
    top: 68,
    left: '50%',
    marginLeft: -65.5,
    width: 131,
    height: 5,
    backgroundColor: theme.colors.text,
    borderRadius: 2.5,
  },
  navigationContent: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingTop: 8,
    height: 60,
  },
  activeNavItem: {
    alignItems: 'center',
    gap: 2,
    position: 'relative',
  },
  navItem: {
    alignItems: 'center',
    gap: 2,
  },
  activeNavText: {
    fontSize: 12,
    fontWeight: '600',
    color: theme.colors.text,
    textAlign: 'center',
  },
  navText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#8A9E96',
  },
  activeNavLine: {
    position: 'absolute',
    top: -8,
    width: 20,
    height: 2,
    backgroundColor: '#2A322F',
    borderRadius: 1,
    alignSelf: 'center',
  },
  bottomSpacing: {
    height: 100,
  },
  verticalLinesContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    zIndex: 0,
  },
  dashedLineContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  dash: {
    width: 4,
    height: 1,
    backgroundColor: '#DEE3E1',
  },
})
